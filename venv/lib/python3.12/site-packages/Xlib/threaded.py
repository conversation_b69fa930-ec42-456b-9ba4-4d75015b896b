# Xlib.threaded -- Import this module to enable threading
#
#    Copyright (C) 2000 <PERSON> <<EMAIL>>
#
#    This program is free software; you can redistribute it and/or modify
#    it under the terms of the GNU General Public License as published by
#    the Free Software Foundation; either version 2 of the License, or
#    (at your option) any later version.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU General Public License for more details.
#
#    You should have received a copy of the GNU General Public License
#    along with this program; if not, write to the Free Software
#    Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA

try:
    # Python 3
    import _thread as thread
except ImportError:
    # Python 2
    import thread

# We change the allocate_lock function in Xlib.support.lock to
# return a basic Python lock, instead of the default dummy lock

from Xlib.support import lock
lock.allocate_lock = thread.allocate_lock
