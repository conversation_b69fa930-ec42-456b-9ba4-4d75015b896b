from datetime import datetime
from app import db

class BackupHistory(db.Model):
    __tablename__ = 'backup_history'

    id = db.Column(db.Integer, primary_key=True)
    job_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>ey('backup_jobs.id'), nullable=False)

    # Informações da execução
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    status = db.Column(db.String(20), nullable=False)  # running, completed, failed, cancelled

    # Estatísticas do backup
    files_processed = db.Column(db.Integer, default=0)
    files_copied = db.Column(db.Integer, default=0)
    files_skipped = db.Column(db.Integer, default=0)
    files_failed = db.Column(db.Integer, default=0)

    # Tamanhos em bytes
    total_size = db.Column(db.BigInteger, default=0)
    compressed_size = db.Column(db.<PERSON>Integer, default=0)

    # Informações adicionais
    backup_path = db.Column(db.String(500))  # Caminho do arquivo de backup gerado
    error_message = db.Column(db.Text)
    log_details = db.Column(db.Text)  # JSON com detalhes do log

    def to_dict(self):
        duration = None
        if self.started_at and self.completed_at:
            duration = (self.completed_at - self.started_at).total_seconds()

        return {
            'id': self.id,
            'job_id': self.job_id,
            'started_at': self.started_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'status': self.status,
            'duration_seconds': duration,
            'files_processed': self.files_processed,
            'files_copied': self.files_copied,
            'files_skipped': self.files_skipped,
            'files_failed': self.files_failed,
            'total_size': self.total_size,
            'compressed_size': self.compressed_size,
            'backup_path': self.backup_path,
            'error_message': self.error_message,
            'log_details': self.log_details
        }
