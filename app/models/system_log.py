from datetime import datetime
from app import db

class SystemLog(db.Model):
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    level = db.Column(db.String(10), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = db.Column(db.Text, nullable=False)
    module = db.Column(db.String(50))  # Módulo que gerou o log
    function = db.Column(db.String(50))  # Função que gerou o log

    # Contexto adicional
    job_id = db.Column(db.Integer, db.<PERSON><PERSON>('backup_jobs.id'))
    history_id = db.Column(db.Integer, db.<PERSON>ey('backup_history.id'))
    extra_data = db.Column(db.Text)  # JSON com dados extras

    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'level': self.level,
            'message': self.message,
            'module': self.module,
            'function': self.function,
            'job_id': self.job_id,
            'history_id': self.history_id,
            'extra_data': self.extra_data,
            'created_at': self.created_at.isoformat()
        }

    @staticmethod
    def log(level, message, module=None, function=None, job_id=None, history_id=None, extra_data=None):
        """Método utilitário para criar logs"""
        log_entry = SystemLog(
            level=level.upper(),
            message=message,
            module=module,
            function=function,
            job_id=job_id,
            history_id=history_id,
            extra_data=extra_data
        )
        db.session.add(log_entry)
        db.session.commit()
        return log_entry
