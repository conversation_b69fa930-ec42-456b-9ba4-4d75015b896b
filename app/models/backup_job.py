from datetime import datetime
from app import db

class BackupJob(db.Model):
    __tablename__ = 'backup_jobs'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    source_path = db.Column(db.String(500), nullable=False)
    destination_path = db.Column(db.String(500), nullable=False)

    # Configurações de agendamento
    schedule_type = db.Column(db.String(20), default='manual')  # manual, daily, weekly, monthly
    schedule_time = db.Column(db.String(10))  # HH:MM
    schedule_days = db.Column(db.String(20))  # Para weekly: 0-6 (seg-dom)

    # Configurações de backup
    compression_enabled = db.Column(db.Boolean, default=True)
    encryption_enabled = db.Column(db.<PERSON>, default=False)
    incremental_backup = db.Column(db.<PERSON>, default=False)
    exclude_patterns = db.Column(db.Text)  # JSON array de padrões a excluir

    # Status e controle
    is_active = db.Column(db.<PERSON>olean, default=True)
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, running, completed, failed

    # Metadados
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relacionamentos
    history = db.relationship('BackupHistory', backref='job', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'source_path': self.source_path,
            'destination_path': self.destination_path,
            'schedule_type': self.schedule_type,
            'schedule_time': self.schedule_time,
            'schedule_days': self.schedule_days,
            'compression_enabled': self.compression_enabled,
            'encryption_enabled': self.encryption_enabled,
            'incremental_backup': self.incremental_backup,
            'exclude_patterns': self.exclude_patterns,
            'is_active': self.is_active,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
