from datetime import datetime
from app import db

class BackupConfig(db.Model):
    __tablename__ = 'backup_config'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    config_type = db.Column(db.String(20), default='string')  # string, integer, boolean, json

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'config_type': self.config_type,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    @staticmethod
    def get_value(key, default=None):
        config = BackupConfig.query.filter_by(key=key).first()
        if config:
            if config.config_type == 'boolean':
                return config.value.lower() == 'true'
            elif config.config_type == 'integer':
                return int(config.value)
            elif config.config_type == 'json':
                import json
                return json.loads(config.value)
            else:
                return config.value
        return default

    @staticmethod
    def set_value(key, value, description=None, config_type='string'):
        config = BackupConfig.query.filter_by(key=key).first()
        if not config:
            config = BackupConfig(key=key)
            db.session.add(config)

        if config_type == 'json':
            import json
            config.value = json.dumps(value)
        else:
            config.value = str(value)

        config.description = description
        config.config_type = config_type
        config.updated_at = datetime.utcnow()

        db.session.commit()
        return config
