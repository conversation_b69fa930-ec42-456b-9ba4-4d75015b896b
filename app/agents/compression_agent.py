import os
import tarfile
import gzip
import shutil
from pathlib import Path

class CompressionAgent:
    """Agente responsável por compressão e descompressão de arquivos"""

    def __init__(self):
        self.compression_methods = {
            'tar.gz': self._create_tar_gz,
            'tar.bz2': self._create_tar_bz2,
            'zip': self._create_zip
        }

    def compress_files(self, file_list, output_path, base_path=None):
        """
        Comprime uma lista de arquivos

        Args:
            file_list: Lista de caminhos de arquivos
            output_path: Caminho do arquivo comprimido de saída
            base_path: Caminho base para caminhos relativos

        Returns:
            int: Tamanho do arquivo comprimido em bytes
        """
        output_path = Path(output_path)

        # Determinar método de compressão pela extensão
        if output_path.suffix == '.gz' and output_path.stem.endswith('.tar'):
            method = 'tar.gz'
        elif output_path.suffix == '.bz2' and output_path.stem.endswith('.tar'):
            method = 'tar.bz2'
        elif output_path.suffix == '.zip':
            method = 'zip'
        else:
            # Padrão: tar.gz
            method = 'tar.gz'
            if not str(output_path).endswith('.tar.gz'):
                output_path = output_path.with_suffix('.tar.gz')

        # Executar compressão
        compression_func = self.compression_methods[method]
        compression_func(file_list, str(output_path), base_path)

        # Retornar tamanho do arquivo comprimido
        return output_path.stat().st_size if output_path.exists() else 0

    def _create_tar_gz(self, file_list, output_path, base_path=None):
        """Cria arquivo tar.gz"""
        with tarfile.open(output_path, 'w:gz') as tar:
            for file_path in file_list:
                if os.path.exists(file_path):
                    # Calcular nome no arquivo
                    if base_path:
                        arcname = os.path.relpath(file_path, base_path)
                    else:
                        arcname = os.path.basename(file_path)

                    tar.add(file_path, arcname=arcname)

    def _create_tar_bz2(self, file_list, output_path, base_path=None):
        """Cria arquivo tar.bz2"""
        with tarfile.open(output_path, 'w:bz2') as tar:
            for file_path in file_list:
                if os.path.exists(file_path):
                    # Calcular nome no arquivo
                    if base_path:
                        arcname = os.path.relpath(file_path, base_path)
                    else:
                        arcname = os.path.basename(file_path)

                    tar.add(file_path, arcname=arcname)

    def _create_zip(self, file_list, output_path, base_path=None):
        """Cria arquivo ZIP"""
        import zipfile

        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_path in file_list:
                if os.path.exists(file_path):
                    # Calcular nome no arquivo
                    if base_path:
                        arcname = os.path.relpath(file_path, base_path)
                    else:
                        arcname = os.path.basename(file_path)

                    zip_file.write(file_path, arcname)

    def extract_archive(self, archive_path, extract_to):
        """
        Extrai um arquivo comprimido

        Args:
            archive_path: Caminho do arquivo comprimido
            extract_to: Diretório de destino

        Returns:
            list: Lista de arquivos extraídos
        """
        archive_path = Path(archive_path)
        extract_to = Path(extract_to)

        # Criar diretório de destino
        extract_to.mkdir(parents=True, exist_ok=True)

        extracted_files = []

        try:
            if archive_path.suffix == '.gz' and archive_path.stem.endswith('.tar'):
                # tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar:
                    tar.extractall(extract_to)
                    extracted_files = tar.getnames()

            elif archive_path.suffix == '.bz2' and archive_path.stem.endswith('.tar'):
                # tar.bz2
                with tarfile.open(archive_path, 'r:bz2') as tar:
                    tar.extractall(extract_to)
                    extracted_files = tar.getnames()

            elif archive_path.suffix == '.zip':
                # ZIP
                import zipfile
                with zipfile.ZipFile(archive_path, 'r') as zip_file:
                    zip_file.extractall(extract_to)
                    extracted_files = zip_file.namelist()

            else:
                raise ValueError(f"Formato de arquivo não suportado: {archive_path.suffix}")

        except Exception as e:
            raise Exception(f"Erro ao extrair arquivo {archive_path}: {str(e)}")

        return extracted_files

    def get_archive_info(self, archive_path):
        """
        Obtém informações sobre um arquivo comprimido

        Args:
            archive_path: Caminho do arquivo comprimido

        Returns:
            dict: Informações do arquivo
        """
        archive_path = Path(archive_path)

        if not archive_path.exists():
            raise FileNotFoundError(f"Arquivo não encontrado: {archive_path}")

        info = {
            'path': str(archive_path),
            'size': archive_path.stat().st_size,
            'files': [],
            'total_files': 0,
            'uncompressed_size': 0
        }

        try:
            if archive_path.suffix == '.gz' and archive_path.stem.endswith('.tar'):
                # tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar:
                    members = tar.getmembers()
                    info['total_files'] = len([m for m in members if m.isfile()])
                    info['uncompressed_size'] = sum(m.size for m in members if m.isfile())
                    info['files'] = [m.name for m in members if m.isfile()]

            elif archive_path.suffix == '.bz2' and archive_path.stem.endswith('.tar'):
                # tar.bz2
                with tarfile.open(archive_path, 'r:bz2') as tar:
                    members = tar.getmembers()
                    info['total_files'] = len([m for m in members if m.isfile()])
                    info['uncompressed_size'] = sum(m.size for m in members if m.isfile())
                    info['files'] = [m.name for m in members if m.isfile()]

            elif archive_path.suffix == '.zip':
                # ZIP
                import zipfile
                with zipfile.ZipFile(archive_path, 'r') as zip_file:
                    file_list = zip_file.filelist
                    info['total_files'] = len(file_list)
                    info['uncompressed_size'] = sum(f.file_size for f in file_list)
                    info['files'] = [f.filename for f in file_list]

        except Exception as e:
            raise Exception(f"Erro ao ler arquivo {archive_path}: {str(e)}")

        return info

    def calculate_compression_ratio(self, original_size, compressed_size):
        """Calcula a taxa de compressão"""
        if original_size == 0:
            return 0

        ratio = (1 - (compressed_size / original_size)) * 100
        return round(ratio, 2)
