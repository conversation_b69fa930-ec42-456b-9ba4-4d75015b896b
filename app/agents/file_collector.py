import os
import fnmatch
from pathlib import Path
from datetime import datetime, timedelta

class FileCollector:
    """Agente responsável por coletar arquivos para backup"""

    def __init__(self):
        self.default_excludes = [
            '*.tmp', '*.temp', '*.log', '*.cache',
            '__pycache__', '.git', '.svn', '.DS_Store',
            'Thumbs.db', '*.pyc', '*.pyo'
        ]

    def collect_files(self, source_path, exclude_patterns=None, incremental=False, last_backup_time=None):
        """
        Coleta arquivos do diretório de origem

        Args:
            source_path: Caminho de origem
            exclude_patterns: Lista de padrões a excluir
            incremental: Se True, coleta apenas arquivos modificados
            last_backup_time: Data do último backup (para backup incremental)

        Returns:
            dict: {'files': [...], 'total_size': int, 'stats': {...}}
        """
        if exclude_patterns is None:
            exclude_patterns = []

        # Combinar padrões de exclusão
        all_excludes = self.default_excludes + exclude_patterns

        files = []
        total_size = 0
        stats = {
            'directories_scanned': 0,
            'files_found': 0,
            'files_excluded': 0,
            'total_size': 0
        }

        # Determinar data de corte para backup incremental
        cutoff_time = None
        if incremental and last_backup_time:
            if isinstance(last_backup_time, str):
                cutoff_time = datetime.fromisoformat(last_backup_time)
            else:
                cutoff_time = last_backup_time

        # Percorrer diretório
        for root, dirs, filenames in os.walk(source_path):
            stats['directories_scanned'] += 1

            # Filtrar diretórios excluídos
            dirs[:] = [d for d in dirs if not self._should_exclude(d, all_excludes)]

            for filename in filenames:
                file_path = os.path.join(root, filename)
                stats['files_found'] += 1

                # Verificar se deve excluir
                if self._should_exclude(filename, all_excludes):
                    stats['files_excluded'] += 1
                    continue

                # Verificar se deve excluir por caminho completo
                rel_path = os.path.relpath(file_path, source_path)
                if self._should_exclude(rel_path, all_excludes):
                    stats['files_excluded'] += 1
                    continue

                try:
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)

                    # Verificar backup incremental
                    if incremental and cutoff_time and file_mtime <= cutoff_time:
                        continue

                    files.append(file_path)
                    total_size += file_size

                except (OSError, IOError) as e:
                    # Arquivo inacessível, pular
                    stats['files_excluded'] += 1
                    continue

        stats['total_size'] = total_size

        return {
            'files': files,
            'total_size': total_size,
            'stats': stats
        }

    def _should_exclude(self, path, exclude_patterns):
        """Verifica se um caminho deve ser excluído"""
        for pattern in exclude_patterns:
            if fnmatch.fnmatch(path, pattern):
                return True

            # Verificar se é um padrão de diretório
            if pattern.endswith('/') and path.startswith(pattern[:-1]):
                return True

        return False

    def get_directory_size(self, path):
        """Calcula o tamanho total de um diretório"""
        total_size = 0

        try:
            for root, dirs, files in os.walk(path):
                for filename in files:
                    file_path = os.path.join(root, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue
        except (OSError, IOError):
            pass

        return total_size

    def analyze_directory(self, path):
        """Analisa um diretório e retorna estatísticas detalhadas"""
        stats = {
            'total_files': 0,
            'total_directories': 0,
            'total_size': 0,
            'file_types': {},
            'largest_files': [],
            'oldest_file': None,
            'newest_file': None
        }

        try:
            for root, dirs, files in os.walk(path):
                stats['total_directories'] += len(dirs)

                for filename in files:
                    file_path = os.path.join(root, filename)

                    try:
                        file_stat = os.stat(file_path)
                        file_size = file_stat.st_size
                        file_mtime = datetime.fromtimestamp(file_stat.st_mtime)

                        stats['total_files'] += 1
                        stats['total_size'] += file_size

                        # Analisar extensão
                        ext = Path(filename).suffix.lower()
                        if ext:
                            stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1

                        # Rastrear arquivos maiores
                        if len(stats['largest_files']) < 10:
                            stats['largest_files'].append((file_path, file_size))
                        else:
                            # Substituir o menor se este for maior
                            min_size = min(stats['largest_files'], key=lambda x: x[1])
                            if file_size > min_size[1]:
                                stats['largest_files'].remove(min_size)
                                stats['largest_files'].append((file_path, file_size))

                        # Rastrear datas
                        if stats['oldest_file'] is None or file_mtime < stats['oldest_file']:
                            stats['oldest_file'] = file_mtime

                        if stats['newest_file'] is None or file_mtime > stats['newest_file']:
                            stats['newest_file'] = file_mtime

                    except (OSError, IOError):
                        continue

        except (OSError, IOError):
            pass

        # Ordenar arquivos maiores
        stats['largest_files'].sort(key=lambda x: x[1], reverse=True)

        return stats
