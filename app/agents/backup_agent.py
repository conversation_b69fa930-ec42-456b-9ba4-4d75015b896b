import os
import shutil
import threading
from datetime import datetime
from pathlib import Path
import json

from app import db
from app.models import BackupHistory, SystemLog
from .file_collector import FileCollector
from .compression_agent import CompressionAgent

class BackupAgent:
    """Agente inteligente responsável por executar backups"""

    def __init__(self):
        self.file_collector = FileCollector()
        self.compression_agent = CompressionAgent()
        self.current_backup = None

    def run_backup(self, job):
        """Executa um backup de forma assíncrona"""
        # Criar entrada no histórico
        history = BackupHistory(
            job_id=job.id,
            status='running'
        )
        db.session.add(history)
        db.session.commit()

        # Executar backup em thread separada
        thread = threading.Thread(
            target=self._execute_backup,
            args=(job, history)
        )
        thread.daemon = True
        thread.start()

        return {'history_id': history.id}

    def _execute_backup(self, job, history):
        """Executa o backup propriamente dito"""
        try:
            SystemLog.log('INFO', f'Iniciando backup do job: {job.name}',
                         'backup_agent', '_execute_backup', job.id, history.id)

            # Validar caminhos
            if not os.path.exists(job.source_path):
                raise Exception(f'Caminho de origem não existe: {job.source_path}')

            # Criar diretório de destino se não existir
            dest_dir = Path(job.destination_path)
            dest_dir.mkdir(parents=True, exist_ok=True)

            # Coletar arquivos
            SystemLog.log('INFO', 'Coletando arquivos...',
                         'backup_agent', '_execute_backup', job.id, history.id)

            files_info = self.file_collector.collect_files(
                source_path=job.source_path,
                exclude_patterns=self._parse_exclude_patterns(job.exclude_patterns),
                incremental=job.incremental_backup
            )

            # Atualizar estatísticas
            history.files_processed = len(files_info['files'])
            history.total_size = files_info['total_size']
            db.session.commit()

            # Gerar nome do arquivo de backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{job.name}_{timestamp}"

            if job.compression_enabled:
                backup_path = dest_dir / f"{backup_filename}.tar.gz"

                SystemLog.log('INFO', 'Comprimindo arquivos...',
                             'backup_agent', '_execute_backup', job.id, history.id)

                compressed_size = self.compression_agent.compress_files(
                    files_info['files'],
                    str(backup_path),
                    job.source_path
                )

                history.compressed_size = compressed_size
            else:
                backup_path = dest_dir / backup_filename
                backup_path.mkdir(exist_ok=True)

                SystemLog.log('INFO', 'Copiando arquivos...',
                             'backup_agent', '_execute_backup', job.id, history.id)

                self._copy_files(files_info['files'], job.source_path, str(backup_path))

            # Finalizar backup
            history.backup_path = str(backup_path)
            history.files_copied = len(files_info['files'])
            history.completed_at = datetime.utcnow()
            history.status = 'completed'

            # Atualizar job
            job.last_run = datetime.utcnow()
            job.status = 'completed'

            db.session.commit()

            SystemLog.log('INFO', f'Backup concluído com sucesso: {backup_path}',
                         'backup_agent', '_execute_backup', job.id, history.id)

        except Exception as e:
            # Registrar erro
            error_msg = str(e)
            history.error_message = error_msg
            history.completed_at = datetime.utcnow()
            history.status = 'failed'
            job.status = 'failed'

            db.session.commit()

            SystemLog.log('ERROR', f'Erro no backup: {error_msg}',
                         'backup_agent', '_execute_backup', job.id, history.id)

    def _parse_exclude_patterns(self, exclude_patterns_str):
        """Converte string JSON de padrões de exclusão em lista"""
        if not exclude_patterns_str:
            return []

        try:
            return json.loads(exclude_patterns_str)
        except:
            return []

    def _copy_files(self, files, source_base, dest_base):
        """Copia arquivos mantendo estrutura de diretórios"""
        for file_path in files:
            try:
                # Calcular caminho relativo
                rel_path = os.path.relpath(file_path, source_base)
                dest_path = os.path.join(dest_base, rel_path)

                # Criar diretório de destino
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)

                # Copiar arquivo
                shutil.copy2(file_path, dest_path)

            except Exception as e:
                SystemLog.log('WARNING', f'Erro ao copiar arquivo {file_path}: {str(e)}',
                             'backup_agent', '_copy_files')

    def get_backup_status(self, history_id):
        """Obtém status de um backup em execução"""
        history = BackupHistory.query.get(history_id)
        if history:
            return history.to_dict()
        return None
