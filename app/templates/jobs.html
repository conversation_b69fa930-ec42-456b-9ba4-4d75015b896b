{% extends "base.html" %}

{% block title %}Jobs de Backup - Sistema de Backup{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-tasks me-2"></i>
                Gerenciamento de Jobs de Backup
            </h1>
            <button class="btn btn-primary" onclick="showNewJobModal()">
                <i class="fas fa-plus me-2"></i>
                Novo Job
            </button>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" id="search-jobs" placeholder="Buscar jobs...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filter-status">
            <option value="">Todos os Status</option>
            <option value="active">Ativos</option>
            <option value="inactive">Inativos</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filter-schedule">
            <option value="">Todos os Tipos</option>
            <option value="manual">Manual</option>
            <option value="daily">Diário</option>
            <option value="weekly">Semanal</option>
            <option value="monthly">Mensal</option>
        </select>
    </div>
</div>

<!-- Lista de Jobs -->
<div class="row" id="jobs-container">
    <div class="col-12">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3">Carregando jobs...</p>
        </div>
    </div>
</div>

<!-- Modal para Novo/Editar Job -->
<div class="modal fade" id="jobModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jobModalTitle">
                    <i class="fas fa-plus me-2"></i>
                    Novo Job de Backup
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="jobForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jobName" class="form-label">Nome do Job *</label>
                                <input type="text" class="form-control" id="jobName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jobDescription" class="form-label">Descrição</label>
                                <input type="text" class="form-control" id="jobDescription">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourcePath" class="form-label">Caminho de Origem *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="sourcePath" required>
                                    <button type="button" class="btn btn-outline-secondary" onclick="browseFolder('sourcePath')">
                                        <i class="fas fa-folder-open"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="destinationPath" class="form-label">Caminho de Destino *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="destinationPath" required>
                                    <button type="button" class="btn btn-outline-secondary" onclick="browseFolder('destinationPath')">
                                        <i class="fas fa-folder-open"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="scheduleType" class="form-label">Tipo de Agendamento</label>
                                <select class="form-select" id="scheduleType" onchange="updateScheduleFields()">
                                    <option value="manual">Manual</option>
                                    <option value="daily">Diário</option>
                                    <option value="weekly">Semanal</option>
                                    <option value="monthly">Mensal</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3" id="scheduleTimeGroup" style="display: none;">
                                <label for="scheduleTime" class="form-label">Horário</label>
                                <input type="time" class="form-control" id="scheduleTime" value="02:00">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3" id="scheduleDaysGroup" style="display: none;">
                                <label for="scheduleDays" class="form-label">Dias da Semana</label>
                                <select class="form-select" id="scheduleDays" multiple>
                                    <option value="0">Segunda</option>
                                    <option value="1">Terça</option>
                                    <option value="2">Quarta</option>
                                    <option value="3">Quinta</option>
                                    <option value="4">Sexta</option>
                                    <option value="5">Sábado</option>
                                    <option value="6">Domingo</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="excludePatterns" class="form-label">Padrões de Exclusão</label>
                                <textarea class="form-control" id="excludePatterns" rows="3"
                                    placeholder="Um padrão por linha, ex:&#10;*.tmp&#10;*.log&#10;__pycache__"></textarea>
                                <div class="form-text">Padrões de arquivos/pastas a serem excluídos do backup</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="compressionEnabled" checked>
                                <label class="form-check-label" for="compressionEnabled">
                                    Compressão Habilitada
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="encryptionEnabled">
                                <label class="form-check-label" for="encryptionEnabled">
                                    Criptografia Habilitada
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="incrementalBackup">
                                <label class="form-check-label" for="incrementalBackup">
                                    Backup Incremental
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary" data-original-text="Salvar Job">
                        <i class="fas fa-save me-2"></i>
                        Salvar Job
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Navegação de Pastas -->
<div class="modal fade" id="folderBrowserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-folder-open me-2"></i>
                    Selecionar Pasta
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Navegação -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="currentPath" readonly>
                            <button type="button" class="btn btn-outline-secondary" onclick="goToPath()">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle w-100" type="button"
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-hdd me-2"></i>Drives
                            </button>
                            <ul class="dropdown-menu w-100" id="drivesMenu">
                                <li><a class="dropdown-item" href="#" onclick="loadDrives()">
                                    <i class="fas fa-sync me-2"></i>Carregar Drives
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Lista de arquivos -->
                <div class="border rounded" style="height: 400px; overflow-y: auto;">
                    <div id="fileList" class="list-group list-group-flush">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-3">Carregando diretório...</p>
                        </div>
                    </div>
                </div>

                <!-- Caminho selecionado -->
                <div class="mt-3">
                    <label class="form-label">Caminho selecionado:</label>
                    <input type="text" class="form-control" id="selectedPath" readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="selectCurrentPath()">
                    <i class="fas fa-check me-2"></i>
                    Selecionar Pasta
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentJobs = [];
let editingJobId = null;

// Carregar jobs ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    loadJobs();

    // Configurar filtros
    document.getElementById('search-jobs').addEventListener('input', filterJobs);
    document.getElementById('filter-status').addEventListener('change', filterJobs);
    document.getElementById('filter-schedule').addEventListener('change', filterJobs);

    // Configurar formulário
    document.getElementById('jobForm').addEventListener('submit', saveJob);
});

function loadJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(jobs => {
            currentJobs = jobs;
            displayJobs(jobs);
        })
        .catch(error => {
            console.error('Erro ao carregar jobs:', error);
            showAlert('Erro ao carregar jobs: ' + error.message, 'danger');
        });
}

function displayJobs(jobs) {
    const container = document.getElementById('jobs-container');

    if (jobs.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h4>Nenhum job encontrado</h4>
                    <p class="text-muted">Crie seu primeiro job de backup clicando no botão "Novo Job"</p>
                    <button class="btn btn-primary" onclick="showNewJobModal()">
                        <i class="fas fa-plus me-2"></i>
                        Criar Primeiro Job
                    </button>
                </div>
            </div>
        `;
        return;
    }

    container.innerHTML = jobs.map(job => createJobCard(job)).join('');
}

function createJobCard(job) {
    const statusClass = job.is_active ? 'active' : 'inactive';
    const statusBadge = job.is_active ?
        '<span class="badge bg-success">Ativo</span>' :
        '<span class="badge bg-secondary">Inativo</span>';

    const nextRun = job.next_run ?
        `<small class="text-muted">Próxima execução: ${formatDateTime(job.next_run)}</small>` :
        '<small class="text-muted">Execução manual</small>';

    return `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card job-card ${statusClass}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${job.name}</h6>
                    ${statusBadge}
                </div>
                <div class="card-body">
                    <p class="card-text">${job.description || 'Sem descrição'}</p>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-folder me-1"></i>
                            ${job.source_path}
                        </small>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-arrow-right me-1"></i>
                            ${job.destination_path}
                        </small>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${getScheduleText(job)}
                        </small>
                    </div>
                    ${nextRun}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="runJob(${job.id})"
                                ${job.is_active ? '' : 'disabled'}>
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="editJob(${job.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-${job.is_active ? 'warning' : 'success'} btn-sm"
                                onclick="toggleJob(${job.id})">
                            <i class="fas fa-${job.is_active ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteJob(${job.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getScheduleText(job) {
    switch(job.schedule_type) {
        case 'daily': return `Diário às ${job.schedule_time || '02:00'}`;
        case 'weekly': return `Semanal às ${job.schedule_time || '02:00'}`;
        case 'monthly': return `Mensal às ${job.schedule_time || '02:00'}`;
        default: return 'Manual';
    }
}

function showNewJobModal() {
    editingJobId = null;
    document.getElementById('jobModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Novo Job de Backup';
    document.getElementById('jobForm').reset();
    updateScheduleFields();

    const modal = new bootstrap.Modal(document.getElementById('jobModal'));
    modal.show();
}

function editJob(jobId) {
    const job = currentJobs.find(j => j.id === jobId);
    if (!job) return;

    editingJobId = jobId;
    document.getElementById('jobModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Editar Job';

    // Preencher formulário
    document.getElementById('jobName').value = job.name;
    document.getElementById('jobDescription').value = job.description || '';
    document.getElementById('sourcePath').value = job.source_path;
    document.getElementById('destinationPath').value = job.destination_path;
    document.getElementById('scheduleType').value = job.schedule_type;
    document.getElementById('scheduleTime').value = job.schedule_time || '02:00';
    document.getElementById('compressionEnabled').checked = job.compression_enabled;
    document.getElementById('encryptionEnabled').checked = job.encryption_enabled;
    document.getElementById('incrementalBackup').checked = job.incremental_backup;

    if (job.exclude_patterns) {
        try {
            const patterns = JSON.parse(job.exclude_patterns);
            document.getElementById('excludePatterns').value = patterns.join('\n');
        } catch (e) {
            document.getElementById('excludePatterns').value = job.exclude_patterns;
        }
    }

    updateScheduleFields();

    const modal = new bootstrap.Modal(document.getElementById('jobModal'));
    modal.show();
}

function updateScheduleFields() {
    const scheduleType = document.getElementById('scheduleType').value;
    const timeGroup = document.getElementById('scheduleTimeGroup');
    const daysGroup = document.getElementById('scheduleDaysGroup');

    if (scheduleType === 'manual') {
        timeGroup.style.display = 'none';
        daysGroup.style.display = 'none';
    } else if (scheduleType === 'weekly') {
        timeGroup.style.display = 'block';
        daysGroup.style.display = 'block';
    } else {
        timeGroup.style.display = 'block';
        daysGroup.style.display = 'none';
    }
}

function saveJob(event) {
    event.preventDefault();

    if (!validateForm(event.target)) {
        return;
    }

    setFormLoading('jobForm', true);

    const formData = {
        name: document.getElementById('jobName').value,
        description: document.getElementById('jobDescription').value,
        source_path: document.getElementById('sourcePath').value,
        destination_path: document.getElementById('destinationPath').value,
        schedule_type: document.getElementById('scheduleType').value,
        schedule_time: document.getElementById('scheduleTime').value,
        compression_enabled: document.getElementById('compressionEnabled').checked,
        encryption_enabled: document.getElementById('encryptionEnabled').checked,
        incremental_backup: document.getElementById('incrementalBackup').checked
    };

    // Processar padrões de exclusão
    const excludeText = document.getElementById('excludePatterns').value.trim();
    if (excludeText) {
        const patterns = excludeText.split('\n').map(p => p.trim()).filter(p => p);
        formData.exclude_patterns = JSON.stringify(patterns);
    }

    // Processar dias da semana para agendamento semanal
    if (formData.schedule_type === 'weekly') {
        const selectedDays = Array.from(document.getElementById('scheduleDays').selectedOptions)
            .map(option => option.value);
        formData.schedule_days = selectedDays.join(',');
    }

    const url = editingJobId ? `/api/jobs/${editingJobId}` : '/api/jobs';
    const method = editingJobId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }

        showAlert(`Job ${editingJobId ? 'atualizado' : 'criado'} com sucesso!`, 'success');
        bootstrap.Modal.getInstance(document.getElementById('jobModal')).hide();
        loadJobs();
    })
    .catch(error => {
        showAlert('Erro ao salvar job: ' + error.message, 'danger');
    })
    .finally(() => {
        setFormLoading('jobForm', false);
    });
}

function runJob(jobId) {
    if (confirm('Deseja executar este job agora?')) {
        fetch(`/api/jobs/${jobId}/run`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                showAlert('Backup iniciado com sucesso!', 'success');
                loadJobs();
            })
            .catch(error => {
                showAlert('Erro ao executar job: ' + error.message, 'danger');
            });
    }
}

function toggleJob(jobId) {
    const job = currentJobs.find(j => j.id === jobId);
    const action = job.is_active ? 'desativar' : 'ativar';

    if (confirm(`Deseja ${action} este job?`)) {
        fetch(`/api/jobs/${jobId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ is_active: !job.is_active })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            showAlert(`Job ${action}do com sucesso!`, 'success');
            loadJobs();
        })
        .catch(error => {
            showAlert(`Erro ao ${action} job: ` + error.message, 'danger');
        });
    }
}

function deleteJob(jobId) {
    if (confirm('Tem certeza que deseja deletar este job? Esta ação não pode ser desfeita.')) {
        fetch(`/api/jobs/${jobId}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                showAlert('Job deletado com sucesso!', 'success');
                loadJobs();
            })
            .catch(error => {
                showAlert('Erro ao deletar job: ' + error.message, 'danger');
            });
    }
}

function filterJobs() {
    const searchTerm = document.getElementById('search-jobs').value.toLowerCase();
    const statusFilter = document.getElementById('filter-status').value;
    const scheduleFilter = document.getElementById('filter-schedule').value;

    const filteredJobs = currentJobs.filter(job => {
        const matchesSearch = job.name.toLowerCase().includes(searchTerm) ||
                            (job.description && job.description.toLowerCase().includes(searchTerm));

        const matchesStatus = !statusFilter ||
                            (statusFilter === 'active' && job.is_active) ||
                            (statusFilter === 'inactive' && !job.is_active);

        const matchesSchedule = !scheduleFilter || job.schedule_type === scheduleFilter;

        return matchesSearch && matchesStatus && matchesSchedule;
    });

    displayJobs(filteredJobs);
}

// Variáveis globais para navegação de pastas
let currentBrowsingPath = '';
let targetInputId = '';

function browseFolder(inputId) {
    targetInputId = inputId;

    // Obter caminho atual do input ou usar home
    const currentValue = document.getElementById(inputId).value;
    currentBrowsingPath = currentValue || '';

    // Abrir modal
    const modal = new bootstrap.Modal(document.getElementById('folderBrowserModal'));
    modal.show();

    // Carregar diretório inicial
    if (currentBrowsingPath) {
        loadDirectory(currentBrowsingPath);
    } else {
        loadDirectory(''); // Carregará o home directory
    }

    // Carregar drives
    loadDrives();
}

function loadDirectory(path = '') {
    const fileList = document.getElementById('fileList');
    const currentPathInput = document.getElementById('currentPath');
    const selectedPathInput = document.getElementById('selectedPath');

    // Mostrar loading
    fileList.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3">Carregando diretório...</p>
        </div>
    `;

    // Fazer requisição para API
    const url = path ? `/api/filesystem/browse?path=${encodeURIComponent(path)}` : '/api/filesystem/browse';

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            currentBrowsingPath = data.current_path;
            currentPathInput.value = currentBrowsingPath;
            selectedPathInput.value = currentBrowsingPath;

            displayDirectoryContents(data.items);
        })
        .catch(error => {
            console.error('Erro ao carregar diretório:', error);
            fileList.innerHTML = `
                <div class="text-center py-5 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>Erro ao carregar diretório:</p>
                    <p><small>${error.message}</small></p>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadDirectory('')">
                        <i class="fas fa-home me-1"></i>Ir para Home
                    </button>
                </div>
            `;
        });
}

function displayDirectoryContents(items) {
    const fileList = document.getElementById('fileList');

    if (items.length === 0) {
        fileList.innerHTML = `
            <div class="text-center py-5 text-muted">
                <i class="fas fa-folder-open fa-2x mb-3"></i>
                <p>Diretório vazio</p>
            </div>
        `;
        return;
    }

    fileList.innerHTML = items.map(item => {
        const icon = item.type === 'directory' ? 'fas fa-folder' : 'fas fa-file';
        const iconColor = item.type === 'directory' ? 'text-warning' : 'text-muted';
        const size = item.type === 'directory' ? '' : formatBytes(item.size);
        const modified = item.modified ? formatDateTime(item.modified) : '';

        return `
            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                 onclick="selectItem('${item.path}', '${item.type}')" style="cursor: pointer;">
                <div class="d-flex align-items-center">
                    <i class="${icon} ${iconColor} me-3"></i>
                    <div>
                        <div class="fw-medium">${item.name}</div>
                        ${modified ? `<small class="text-muted">${modified}</small>` : ''}
                    </div>
                </div>
                <div class="text-end">
                    ${size ? `<small class="text-muted">${size}</small>` : ''}
                    ${item.permissions ? `<br><small class="text-muted">${item.permissions}</small>` : ''}
                </div>
            </div>
        `;
    }).join('');
}

function selectItem(path, type) {
    if (type === 'directory') {
        // Navegar para o diretório
        loadDirectory(path);
    } else {
        // Selecionar o diretório pai do arquivo
        const parentPath = path.substring(0, path.lastIndexOf('/')) || path.substring(0, path.lastIndexOf('\\'));
        document.getElementById('selectedPath').value = parentPath;
    }
}

function loadDrives() {
    fetch('/api/filesystem/drives')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            const drivesMenu = document.getElementById('drivesMenu');
            drivesMenu.innerHTML = data.drives.map(drive => `
                <li><a class="dropdown-item" href="#" onclick="loadDirectory('${drive.path}')">
                    <i class="fas fa-hdd me-2"></i>${drive.name}
                </a></li>
            `).join('');

            if (data.drives.length === 0) {
                drivesMenu.innerHTML = '<li><span class="dropdown-item-text text-muted">Nenhum drive encontrado</span></li>';
            }
        })
        .catch(error => {
            console.error('Erro ao carregar drives:', error);
            const drivesMenu = document.getElementById('drivesMenu');
            drivesMenu.innerHTML = '<li><span class="dropdown-item-text text-danger">Erro ao carregar drives</span></li>';
        });
}

function goToPath() {
    const pathInput = document.getElementById('currentPath');
    const path = pathInput.value.trim();

    if (path) {
        loadDirectory(path);
    }
}

function selectCurrentPath() {
    const selectedPath = document.getElementById('selectedPath').value;

    if (selectedPath && targetInputId) {
        document.getElementById(targetInputId).value = selectedPath;

        // Fechar modal
        bootstrap.Modal.getInstance(document.getElementById('folderBrowserModal')).hide();

        showAlert('Pasta selecionada com sucesso!', 'success', 2000);
    } else {
        showAlert('Selecione uma pasta válida', 'warning');
    }
}
</script>
{% endblock %}
