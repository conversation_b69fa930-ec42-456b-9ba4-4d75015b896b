{% extends "base.html" %}

{% block title %}Configurações - Sistema de Backup{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog me-2"></i>
            Configurações do Sistema
        </h1>
    </div>
</div>

<div class="row">
    <!-- <PERSON><PERSON> de Configurações -->
    <div class="col-md-3">
        <div class="list-group" id="settings-menu">
            <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#general-settings">
                <i class="fas fa-cogs me-2"></i>Geral
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#backup-settings">
                <i class="fas fa-shield-alt me-2"></i>Backup
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#notification-settings">
                <i class="fas fa-bell me-2"></i>Notificações
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#security-settings">
                <i class="fas fa-lock me-2"></i>Segurança
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#system-settings">
                <i class="fas fa-server me-2"></i>Sistema
            </a>
        </div>
    </div>

    <!-- Conteúdo das Configurações -->
    <div class="col-md-9">
        <div class="tab-content">
            <!-- Configurações Gerais -->
            <div class="tab-pane fade show active" id="general-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configurações Gerais</h5>
                    </div>
                    <div class="card-body">
                        <form id="general-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Nome do Sistema</label>
                                        <input type="text" class="form-control" id="system-name" value="Sistema de Backup Inteligente">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone">
                                            <option value="America/Sao_Paulo">America/São Paulo</option>
                                            <option value="UTC">UTC</option>
                                            <option value="America/New_York">America/New York</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Idioma</label>
                                        <select class="form-select" id="language">
                                            <option value="pt-BR">Português (Brasil)</option>
                                            <option value="en-US">English (US)</option>
                                            <option value="es-ES">Español</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Tema</label>
                                        <select class="form-select" id="theme">
                                            <option value="light">Claro</option>
                                            <option value="dark">Escuro</option>
                                            <option value="auto">Automático</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Salvar Configurações
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Configurações de Backup -->
            <div class="tab-pane fade" id="backup-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configurações de Backup</h5>
                    </div>
                    <div class="card-body">
                        <form id="backup-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Diretório Padrão de Backup</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="default-backup-dir" value="/backup">
                                            <button type="button" class="btn btn-outline-secondary" onclick="browseFolder('default-backup-dir')">
                                                <i class="fas fa-folder-open"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Retenção de Backups (dias)</label>
                                        <input type="number" class="form-control" id="backup-retention" value="30" min="1" max="365">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Compressão Padrão</label>
                                        <select class="form-select" id="default-compression">
                                            <option value="tar.gz">tar.gz (Recomendado)</option>
                                            <option value="tar.bz2">tar.bz2 (Maior compressão)</option>
                                            <option value="zip">ZIP (Compatibilidade)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Threads Simultâneas</label>
                                        <input type="number" class="form-control" id="max-threads" value="4" min="1" max="16">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Padrões de Exclusão Globais</label>
                                <textarea class="form-control" id="global-excludes" rows="4"
                                    placeholder="Um padrão por linha, ex:&#10;*.tmp&#10;*.log&#10;__pycache__">*.tmp
*.log
*.cache
__pycache__
.git
.svn
Thumbs.db
.DS_Store</textarea>
                                <div class="form-text">Estes padrões serão aplicados a todos os backups por padrão</div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-compression" checked>
                                        <label class="form-check-label">Compressão habilitada por padrão</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-verification">
                                        <label class="form-check-label">Verificar integridade após backup</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-incremental">
                                        <label class="form-check-label">Backup incremental por padrão</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary mt-3">
                                <i class="fas fa-save me-2"></i>Salvar Configurações
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Configurações de Notificações -->
            <div class="tab-pane fade" id="notification-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configurações de Notificações</h5>
                    </div>
                    <div class="card-body">
                        <form id="notification-form">
                            <div class="mb-4">
                                <h6>Email</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Servidor SMTP</label>
                                            <input type="text" class="form-control" id="smtp-server" placeholder="smtp.gmail.com">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Porta SMTP</label>
                                            <input type="number" class="form-control" id="smtp-port" value="587">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Email de Envio</label>
                                            <input type="email" class="form-control" id="smtp-email" placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Senha</label>
                                            <input type="password" class="form-control" id="smtp-password">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Destinatários</label>
                                    <input type="text" class="form-control" id="notification-recipients"
                                           placeholder="<EMAIL>, <EMAIL>">
                                    <div class="form-text">Separar múltiplos emails com vírgula</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h6>Eventos para Notificação</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify-success" checked>
                                            <label class="form-check-label">Backup concluído com sucesso</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify-failure" checked>
                                            <label class="form-check-label">Falha no backup</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify-start">
                                            <label class="form-check-label">Início do backup</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify-warning" checked>
                                            <label class="form-check-label">Avisos do sistema</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Salvar Configurações
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="testNotification()">
                                <i class="fas fa-paper-plane me-2"></i>Testar Email
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Configurações de Segurança -->
            <div class="tab-pane fade" id="security-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configurações de Segurança</h5>
                    </div>
                    <div class="card-body">
                        <form id="security-form">
                            <div class="mb-4">
                                <h6>Criptografia</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Algoritmo de Criptografia</label>
                                            <select class="form-select" id="encryption-algorithm">
                                                <option value="AES-256">AES-256 (Recomendado)</option>
                                                <option value="AES-128">AES-128</option>
                                                <option value="ChaCha20">ChaCha20</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Chave de Criptografia</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="encryption-key">
                                                <button type="button" class="btn btn-outline-secondary" onclick="generateKey()">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h6>Controle de Acesso</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="require-auth">
                                            <label class="form-check-label">Exigir autenticação</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-2fa">
                                            <label class="form-check-label">Autenticação de dois fatores</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="log-access" checked>
                                            <label class="form-check-label">Registrar acessos</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="restrict-ip">
                                            <label class="form-check-label">Restringir por IP</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Salvar Configurações
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Configurações do Sistema -->
            <div class="tab-pane fade" id="system-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configurações do Sistema</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informações do Sistema</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Versão:</strong></td><td>1.0.0</td></tr>
                                    <tr><td><strong>Python:</strong></td><td id="python-version">-</td></tr>
                                    <tr><td><strong>Flask:</strong></td><td id="flask-version">-</td></tr>
                                    <tr><td><strong>Banco de Dados:</strong></td><td>SQLite</td></tr>
                                    <tr><td><strong>Uptime:</strong></td><td id="system-uptime">-</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Recursos do Sistema</h6>
                                <div class="mb-3">
                                    <label class="form-label">CPU</label>
                                    <div class="progress">
                                        <div class="progress-bar" id="cpu-usage-bar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Memória</label>
                                    <div class="progress">
                                        <div class="progress-bar" id="memory-usage-bar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Disco</label>
                                    <div class="progress">
                                        <div class="progress-bar" id="disk-usage-bar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h6>Ações do Sistema</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
                                        <i class="fas fa-download me-2"></i>Exportar Configurações
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="importSettings()">
                                        <i class="fas fa-upload me-2"></i>Importar Configurações
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                                        <i class="fas fa-undo me-2"></i>Restaurar Padrões
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="restartSystem()">
                                        <i class="fas fa-power-off me-2"></i>Reiniciar Sistema
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadSystemInfo();

    // Configurar formulários
    document.getElementById('general-form').addEventListener('submit', saveGeneralSettings);
    document.getElementById('backup-form').addEventListener('submit', saveBackupSettings);
    document.getElementById('notification-form').addEventListener('submit', saveNotificationSettings);
    document.getElementById('security-form').addEventListener('submit', saveSecuritySettings);

    // Atualizar informações do sistema periodicamente
    setInterval(loadSystemInfo, 30000);
});

function loadSettings() {
    // Carregar configurações da API
    fetch('/api/config')
        .then(response => response.json())
        .then(configs => {
            configs.forEach(config => {
                const element = document.getElementById(config.key.replace('_', '-'));
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = config.value === 'true';
                    } else {
                        element.value = config.value;
                    }
                }
            });
        })
        .catch(error => {
            console.error('Erro ao carregar configurações:', error);
        });
}

function loadSystemInfo() {
    // Simular informações do sistema
    document.getElementById('python-version').textContent = '3.11.0';
    document.getElementById('flask-version').textContent = '2.3.0';
    document.getElementById('system-uptime').textContent = '2 dias, 14 horas';

    // Simular uso de recursos
    const cpu = Math.random() * 100;
    const memory = Math.random() * 100;
    const disk = Math.random() * 100;

    updateProgressBar('cpu-usage-bar', cpu);
    updateProgressBar('memory-usage-bar', memory);
    updateProgressBar('disk-usage-bar', disk);
}

function updateProgressBar(id, value) {
    const bar = document.getElementById(id);
    bar.style.width = value + '%';
    bar.textContent = value.toFixed(1) + '%';

    // Alterar cor baseado no valor
    bar.className = 'progress-bar';
    if (value > 80) {
        bar.classList.add('bg-danger');
    } else if (value > 60) {
        bar.classList.add('bg-warning');
    } else {
        bar.classList.add('bg-success');
    }
}

function saveGeneralSettings(event) {
    event.preventDefault();

    const settings = {
        'system_name': document.getElementById('system-name').value,
        'timezone': document.getElementById('timezone').value,
        'language': document.getElementById('language').value,
        'theme': document.getElementById('theme').value
    };

    saveSettings(settings, 'Configurações gerais salvas com sucesso!');
}

function saveBackupSettings(event) {
    event.preventDefault();

    const settings = {
        'default_backup_dir': document.getElementById('default-backup-dir').value,
        'backup_retention': document.getElementById('backup-retention').value,
        'default_compression': document.getElementById('default-compression').value,
        'max_threads': document.getElementById('max-threads').value,
        'global_excludes': document.getElementById('global-excludes').value,
        'enable_compression': document.getElementById('enable-compression').checked,
        'enable_verification': document.getElementById('enable-verification').checked,
        'enable_incremental': document.getElementById('enable-incremental').checked
    };

    saveSettings(settings, 'Configurações de backup salvas com sucesso!');
}

function saveNotificationSettings(event) {
    event.preventDefault();

    const settings = {
        'smtp_server': document.getElementById('smtp-server').value,
        'smtp_port': document.getElementById('smtp-port').value,
        'smtp_email': document.getElementById('smtp-email').value,
        'smtp_password': document.getElementById('smtp-password').value,
        'notification_recipients': document.getElementById('notification-recipients').value,
        'notify_success': document.getElementById('notify-success').checked,
        'notify_failure': document.getElementById('notify-failure').checked,
        'notify_start': document.getElementById('notify-start').checked,
        'notify_warning': document.getElementById('notify-warning').checked
    };

    saveSettings(settings, 'Configurações de notificação salvas com sucesso!');
}

function saveSecuritySettings(event) {
    event.preventDefault();

    const settings = {
        'encryption_algorithm': document.getElementById('encryption-algorithm').value,
        'encryption_key': document.getElementById('encryption-key').value,
        'require_auth': document.getElementById('require-auth').checked,
        'enable_2fa': document.getElementById('enable-2fa').checked,
        'log_access': document.getElementById('log-access').checked,
        'restrict_ip': document.getElementById('restrict-ip').checked
    };

    saveSettings(settings, 'Configurações de segurança salvas com sucesso!');
}

function saveSettings(settings, successMessage) {
    const promises = Object.entries(settings).map(([key, value]) => {
        return fetch(`/api/config/${key}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                value: value,
                config_type: typeof value === 'boolean' ? 'boolean' : 'string'
            })
        });
    });

    Promise.all(promises)
        .then(() => {
            showAlert(successMessage, 'success');
        })
        .catch(error => {
            showAlert('Erro ao salvar configurações: ' + error.message, 'danger');
        });
}

function testNotification() {
    showAlert('Funcionalidade de teste de email será implementada', 'info');
}

function generateKey() {
    const key = Array.from(crypto.getRandomValues(new Uint8Array(32)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    document.getElementById('encryption-key').value = key;
    showAlert('Chave de criptografia gerada', 'success', 2000);
}

function exportSettings() {
    showAlert('Funcionalidade de exportação será implementada', 'info');
}

function importSettings() {
    showAlert('Funcionalidade de importação será implementada', 'info');
}

function resetSettings() {
    if (confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
        showAlert('Funcionalidade de reset será implementada', 'info');
    }
}

function restartSystem() {
    if (confirm('Tem certeza que deseja reiniciar o sistema? Todos os backups em execução serão interrompidos.')) {
        showAlert('Funcionalidade de reinicialização será implementada', 'info');
    }
}
</script>
{% endblock %}
