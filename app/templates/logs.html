{% extends "base.html" %}

{% block title %}Logs do Sistema - Sistema de Backup{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-file-alt me-2"></i>
            Logs do Sistema
        </h1>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-md-2">
        <select class="form-select" id="filter-level">
            <option value="">Todos os Níveis</option>
            <option value="DEBUG">Debug</option>
            <option value="INFO">Info</option>
            <option value="WARNING">Warning</option>
            <option value="ERROR">Error</option>
            <option value="CRITICAL">Critical</option>
        </select>
    </div>
    <div class="col-md-3">
        <input type="text" class="form-control" id="filter-module" placeholder="Filtrar por módulo...">
    </div>
    <div class="col-md-3">
        <input type="text" class="form-control" id="filter-message" placeholder="Buscar na mensagem...">
    </div>
    <div class="col-md-2">
        <input type="date" class="form-control" id="filter-date">
    </div>
    <div class="col-md-2">
        <button class="btn btn-outline-secondary w-100" onclick="clearLogFilters()">
            <i class="fas fa-times me-1"></i>Limpar
        </button>
    </div>
</div>

<!-- Controles -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
            <label class="form-check-label" for="autoRefresh">
                Atualização automática (30s)
            </label>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshLogs()">
            <i class="fas fa-sync-alt me-1"></i>Atualizar
        </button>
        <button class="btn btn-outline-success btn-sm me-2" onclick="exportLogs()">
            <i class="fas fa-download me-1"></i>Exportar
        </button>
        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs()">
            <i class="fas fa-trash me-1"></i>Limpar Logs
        </button>
    </div>
</div>

<!-- Logs -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Entradas de Log</h5>
        <span class="badge bg-secondary" id="log-count">0 entradas</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
            <table class="table table-sm table-hover mb-0">
                <thead class="table-light sticky-top">
                    <tr>
                        <th style="width: 120px;">Timestamp</th>
                        <th style="width: 80px;">Nível</th>
                        <th style="width: 120px;">Módulo</th>
                        <th>Mensagem</th>
                        <th style="width: 60px;">Ações</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-3 mb-0">Carregando logs...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal de Detalhes do Log -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Detalhes do Log
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="log-details-content">
                <!-- Conteúdo será inserido aqui -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let autoRefreshInterval;
let currentLogFilters = {};

document.addEventListener('DOMContentLoaded', function() {
    loadLogs();

    // Configurar filtros
    document.getElementById('filter-level').addEventListener('change', applyLogFilters);
    document.getElementById('filter-module').addEventListener('input', debounce(applyLogFilters, 500));
    document.getElementById('filter-message').addEventListener('input', debounce(applyLogFilters, 500));
    document.getElementById('filter-date').addEventListener('change', applyLogFilters);

    // Configurar auto-refresh
    document.getElementById('autoRefresh').addEventListener('change', toggleAutoRefresh);
    toggleAutoRefresh(); // Iniciar auto-refresh se estiver marcado
});

function loadLogs() {
    // Construir URL com filtros
    const params = new URLSearchParams({
        per_page: 100,
        ...currentLogFilters
    });

    fetch(`/api/logs?${params}`)
        .then(response => response.json())
        .then(data => {
            displayLogs(data.items);
            updateLogCount(data.total);
        })
        .catch(error => {
            console.error('Erro ao carregar logs:', error);
            showAlert('Erro ao carregar logs: ' + error.message, 'danger');
        });
}

function displayLogs(logs) {
    const tbody = document.getElementById('logs-table-body');

    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-4">
                    <i class="fas fa-file-alt fa-2x text-muted mb-3"></i>
                    <p class="mb-0">Nenhum log encontrado</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = logs.map(log => {
        const levelClass = getLevelClass(log.level);
        const levelBadge = `<span class="badge bg-${levelClass}">${log.level}</span>`;
        const timestamp = formatDateTime(log.created_at);
        const module = log.module || 'Sistema';
        const message = truncateText(log.message, 100);

        return `
            <tr class="log-entry" data-level="${log.level}">
                <td>
                    <small class="text-muted">${timestamp}</small>
                </td>
                <td>${levelBadge}</td>
                <td>
                    <small class="text-muted">${module}</small>
                    ${log.function ? `<br><small class="text-muted">${log.function}()</small>` : ''}
                </td>
                <td>
                    <div class="log-message">${message}</div>
                    ${log.job_id ? `<small class="text-info">Job: ${log.job_id}</small>` : ''}
                </td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="showLogDetails(${log.id})"
                            title="Ver Detalhes">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function getLevelClass(level) {
    const classes = {
        'DEBUG': 'secondary',
        'INFO': 'info',
        'WARNING': 'warning',
        'ERROR': 'danger',
        'CRITICAL': 'dark'
    };
    return classes[level] || 'secondary';
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function updateLogCount(total) {
    document.getElementById('log-count').textContent = `${total} entradas`;
}

function applyLogFilters() {
    currentLogFilters = {};

    const level = document.getElementById('filter-level').value;
    const module = document.getElementById('filter-module').value.trim();
    const message = document.getElementById('filter-message').value.trim();
    const date = document.getElementById('filter-date').value;

    if (level) currentLogFilters.level = level;
    if (module) currentLogFilters.module = module;
    if (message) currentLogFilters.message = message;
    if (date) currentLogFilters.date = date;

    loadLogs();
}

function clearLogFilters() {
    document.getElementById('filter-level').value = '';
    document.getElementById('filter-module').value = '';
    document.getElementById('filter-message').value = '';
    document.getElementById('filter-date').value = '';

    currentLogFilters = {};
    loadLogs();
}

function refreshLogs() {
    loadLogs();
    showAlert('Logs atualizados', 'success', 2000);
}

function toggleAutoRefresh() {
    const autoRefresh = document.getElementById('autoRefresh').checked;

    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }

    if (autoRefresh) {
        autoRefreshInterval = setInterval(loadLogs, 30000); // 30 segundos
    }
}

function showLogDetails(logId) {
    // Simular busca de detalhes (implementar endpoint específico se necessário)
    fetch(`/api/logs`)
        .then(response => response.json())
        .then(data => {
            const log = data.items.find(l => l.id === logId);
            if (!log) {
                throw new Error('Log não encontrado');
            }

            const content = document.getElementById('log-details-content');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informações Gerais</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID:</strong></td><td>${log.id}</td></tr>
                            <tr><td><strong>Nível:</strong></td><td><span class="badge bg-${getLevelClass(log.level)}">${log.level}</span></td></tr>
                            <tr><td><strong>Timestamp:</strong></td><td>${formatDateTime(log.created_at)}</td></tr>
                            <tr><td><strong>Módulo:</strong></td><td>${log.module || 'N/A'}</td></tr>
                            <tr><td><strong>Função:</strong></td><td>${log.function || 'N/A'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Contexto</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Job ID:</strong></td><td>${log.job_id || 'N/A'}</td></tr>
                            <tr><td><strong>History ID:</strong></td><td>${log.history_id || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Mensagem</h6>
                        <div class="alert alert-${getLevelClass(log.level)}">
                            ${log.message}
                        </div>
                    </div>
                </div>

                ${log.extra_data ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Dados Extras</h6>
                            <pre class="bg-light p-3 rounded"><code>${log.extra_data}</code></pre>
                        </div>
                    </div>
                ` : ''}
            `;

            const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
            modal.show();
        })
        .catch(error => {
            showAlert('Erro ao carregar detalhes do log: ' + error.message, 'danger');
        });
}

function exportLogs() {
    showAlert('Funcionalidade de exportação de logs será implementada', 'info');
}

function clearLogs() {
    if (confirm('Tem certeza que deseja limpar todos os logs? Esta ação não pode ser desfeita.')) {
        showAlert('Funcionalidade de limpeza de logs será implementada', 'info');
    }
}

// Função utilitária para debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Limpar interval ao sair da página
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>
{% endblock %}
