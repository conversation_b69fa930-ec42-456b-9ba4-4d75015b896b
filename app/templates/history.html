{% extends "base.html" %}

{% block title %}Histórico de Backups - Sistema de Backup{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-history me-2"></i>
            Histórico de Backups
        </h1>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-md-3">
        <select class="form-select" id="filter-job">
            <option value="">Todos os Jobs</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filter-status">
            <option value="">Todos os Status</option>
            <option value="running">Em Execução</option>
            <option value="completed">Concluído</option>
            <option value="failed">Falhou</option>
            <option value="cancelled">Cancelado</option>
        </select>
    </div>
    <div class="col-md-3">
        <input type="date" class="form-control" id="filter-date" placeholder="Data">
    </div>
    <div class="col-md-3">
        <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
            <i class="fas fa-times me-2"></i>Limpar Filtros
        </button>
    </div>
</div>

<!-- Tabela de Histórico -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Execuções de Backup</h5>
        <div>
            <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshHistory()">
                <i class="fas fa-sync-alt me-1"></i>Atualizar
            </button>
            <button class="btn btn-outline-success btn-sm" onclick="exportHistory()">
                <i class="fas fa-download me-1"></i>Exportar
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Job</th>
                        <th>Status</th>
                        <th>Início</th>
                        <th>Duração</th>
                        <th>Arquivos</th>
                        <th>Tamanho</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="history-table-body">
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                            <p class="mt-3 mb-0">Carregando histórico...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        <nav aria-label="Paginação do histórico">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Paginação será inserida aqui -->
            </ul>
        </nav>
    </div>
</div>

<!-- Modal de Detalhes -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Detalhes do Backup
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="details-content">
                <!-- Conteúdo será inserido aqui -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentPage = 1;
let totalPages = 1;
let currentFilters = {};

document.addEventListener('DOMContentLoaded', function() {
    loadHistory();
    loadJobs(); // Para o filtro de jobs

    // Configurar filtros
    document.getElementById('filter-job').addEventListener('change', applyFilters);
    document.getElementById('filter-status').addEventListener('change', applyFilters);
    document.getElementById('filter-date').addEventListener('change', applyFilters);
});

function loadHistory(page = 1) {
    currentPage = page;

    // Construir URL com filtros
    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        ...currentFilters
    });

    fetch(`/api/history?${params}`)
        .then(response => response.json())
        .then(data => {
            displayHistory(data.items);
            updatePagination(data.current_page, data.pages, data.total);
        })
        .catch(error => {
            console.error('Erro ao carregar histórico:', error);
            showAlert('Erro ao carregar histórico: ' + error.message, 'danger');
        });
}

function loadJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(jobs => {
            const jobFilter = document.getElementById('filter-job');
            jobFilter.innerHTML = '<option value="">Todos os Jobs</option>';

            jobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.id;
                option.textContent = job.name;
                jobFilter.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Erro ao carregar jobs:', error);
        });
}

function displayHistory(items) {
    const tbody = document.getElementById('history-table-body');

    if (items.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-history fa-2x text-muted mb-3"></i>
                    <p class="mb-0">Nenhum backup encontrado</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = items.map(item => {
        const statusBadge = `<span class="badge bg-${getStatusColor(item.status)}">${item.status}</span>`;
        const duration = item.duration_seconds ? formatDuration(item.duration_seconds) : '-';
        const filesInfo = `${item.files_processed || 0} processados`;
        const sizeInfo = formatBytes(item.total_size || 0);

        return `
            <tr>
                <td>
                    <div class="fw-medium">Job #${item.job_id}</div>
                    <small class="text-muted">ID: ${item.id}</small>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div>${formatDateTime(item.started_at)}</div>
                    ${item.completed_at ? `<small class="text-muted">Fim: ${formatDateTime(item.completed_at)}</small>` : ''}
                </td>
                <td>${duration}</td>
                <td>
                    <div>${filesInfo}</div>
                    ${item.files_copied ? `<small class="text-success">${item.files_copied} copiados</small>` : ''}
                    ${item.files_failed ? `<small class="text-danger">${item.files_failed} falharam</small>` : ''}
                </td>
                <td>
                    <div>${sizeInfo}</div>
                    ${item.compressed_size ? `<small class="text-info">Comprimido: ${formatBytes(item.compressed_size)}</small>` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="showDetails(${item.id})"
                                title="Ver Detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${item.backup_path ? `
                            <button class="btn btn-outline-success" onclick="downloadBackup('${item.backup_path}')"
                                    title="Download">
                                <i class="fas fa-download"></i>
                            </button>
                        ` : ''}
                        ${item.status === 'running' ? `
                            <button class="btn btn-outline-warning" onclick="cancelBackup(${item.id})"
                                    title="Cancelar">
                                <i class="fas fa-stop"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function updatePagination(current, total, totalItems) {
    totalPages = total;
    const pagination = document.getElementById('pagination');

    if (total <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // Botão anterior
    paginationHTML += `
        <li class="page-item ${current === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHistory(${current - 1})">Anterior</a>
        </li>
    `;

    // Páginas
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(total, current + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadHistory(${i})">${i}</a>
            </li>
        `;
    }

    // Botão próximo
    paginationHTML += `
        <li class="page-item ${current === total ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHistory(${current + 1})">Próximo</a>
        </li>
    `;

    pagination.innerHTML = paginationHTML;
}

function applyFilters() {
    currentFilters = {};

    const jobId = document.getElementById('filter-job').value;
    const status = document.getElementById('filter-status').value;
    const date = document.getElementById('filter-date').value;

    if (jobId) currentFilters.job_id = jobId;
    if (status) currentFilters.status = status;
    if (date) currentFilters.date = date;

    loadHistory(1); // Voltar para primeira página
}

function clearFilters() {
    document.getElementById('filter-job').value = '';
    document.getElementById('filter-status').value = '';
    document.getElementById('filter-date').value = '';

    currentFilters = {};
    loadHistory(1);
}

function refreshHistory() {
    loadHistory(currentPage);
    showAlert('Histórico atualizado', 'success', 2000);
}

function showDetails(historyId) {
    fetch(`/api/history/${historyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            const content = document.getElementById('details-content');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informações Gerais</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID:</strong></td><td>${data.id}</td></tr>
                            <tr><td><strong>Job ID:</strong></td><td>${data.job_id}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(data.status)}">${data.status}</span></td></tr>
                            <tr><td><strong>Início:</strong></td><td>${formatDateTime(data.started_at)}</td></tr>
                            <tr><td><strong>Fim:</strong></td><td>${data.completed_at ? formatDateTime(data.completed_at) : 'N/A'}</td></tr>
                            <tr><td><strong>Duração:</strong></td><td>${data.duration_seconds ? formatDuration(data.duration_seconds) : 'N/A'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Estatísticas</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Arquivos Processados:</strong></td><td>${data.files_processed || 0}</td></tr>
                            <tr><td><strong>Arquivos Copiados:</strong></td><td>${data.files_copied || 0}</td></tr>
                            <tr><td><strong>Arquivos Ignorados:</strong></td><td>${data.files_skipped || 0}</td></tr>
                            <tr><td><strong>Arquivos com Erro:</strong></td><td>${data.files_failed || 0}</td></tr>
                            <tr><td><strong>Tamanho Total:</strong></td><td>${formatBytes(data.total_size || 0)}</td></tr>
                            <tr><td><strong>Tamanho Comprimido:</strong></td><td>${formatBytes(data.compressed_size || 0)}</td></tr>
                        </table>
                    </div>
                </div>

                ${data.backup_path ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Arquivo de Backup</h6>
                            <div class="alert alert-info">
                                <i class="fas fa-file-archive me-2"></i>
                                <strong>Localização:</strong> ${data.backup_path}
                            </div>
                        </div>
                    </div>
                ` : ''}

                ${data.error_message ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Erro</h6>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ${data.error_message}
                            </div>
                        </div>
                    </div>
                ` : ''}
            `;

            const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
            modal.show();
        })
        .catch(error => {
            showAlert('Erro ao carregar detalhes: ' + error.message, 'danger');
        });
}

function downloadBackup(backupPath) {
    showAlert('Funcionalidade de download será implementada', 'info');
}

function cancelBackup(historyId) {
    if (confirm('Deseja cancelar este backup em execução?')) {
        showAlert('Funcionalidade de cancelamento será implementada', 'info');
    }
}

function exportHistory() {
    showAlert('Funcionalidade de exportação será implementada', 'info');
}
</script>
{% endblock %}
