// Sistema de Backup - JavaScript Principal

// Configurações globais
const API_BASE = '/api';
const REFRESH_INTERVAL = 30000; // 30 segundos

// Utilitários
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('pt-BR');
}

function formatDuration(seconds) {
    if (!seconds) return '-';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

function getStatusColor(status) {
    const colors = {
        'running': 'info',
        'completed': 'success',
        'failed': 'danger',
        'pending': 'warning',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

// Sistema de alertas
function showAlert(message, type = 'info', duration = 5000) {
    const alertsContainer = document.getElementById('alerts-container');
    const alertId = 'alert-' + Date.now();

    const alertHTML = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    alertsContainer.insertAdjacentHTML('beforeend', alertHTML);

    // Auto-remover após duração especificada
    if (duration > 0) {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, duration);
    }
}

function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// API Helper
async function apiRequest(endpoint, options = {}) {
    const url = API_BASE + endpoint;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const config = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, config);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Erro na requisição');
        }

        return data;
    } catch (error) {
        console.error('Erro na API:', error);
        showAlert('Erro na comunicação com o servidor: ' + error.message, 'danger');
        throw error;
    }
}

// Funções do Dashboard
function checkSystemStatus() {
    showAlert('Verificando status do sistema...', 'info', 2000);
    // Implementar verificação real do sistema
    setTimeout(() => {
        showAlert('Sistema funcionando normalmente', 'success');
    }, 1000);
}

function showAbout() {
    const aboutModal = new bootstrap.Modal(document.createElement('div'));
    aboutModal._element.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        Sobre o Sistema
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>Sistema de Backup Inteligente</h6>
                    <p>Versão 1.0.0</p>
                    <p>Sistema automatizado de backup com interface web moderna e agentes inteligentes.</p>
                    <hr>
                    <h6>Recursos:</h6>
                    <ul>
                        <li>Backups automatizados e agendados</li>
                        <li>Compressão e criptografia</li>
                        <li>Interface web responsiva</li>
                        <li>Monitoramento em tempo real</li>
                        <li>Histórico detalhado</li>
                        <li>Sistema de logs</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(aboutModal._element);
    aboutModal.show();

    aboutModal._element.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(aboutModal._element);
    });
}

// Funções de Jobs
function showNewJobModal() {
    window.location.href = '/jobs';
}

function runAllActiveJobs() {
    if (confirm('Deseja executar todos os jobs ativos?')) {
        showAlert('Executando todos os jobs ativos...', 'info');
        // Implementar execução de todos os jobs
    }
}

function showSystemLogs() {
    window.location.href = '/logs';
}

function exportBackupReport() {
    showAlert('Gerando relatório de backup...', 'info');
    // Implementar exportação de relatório
}

// Validação de formulários
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    return isValid;
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    // Ativar tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Ativar popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Marcar link ativo na navegação
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });

    // Adicionar animações aos cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });
});

// Funções de utilidade para formulários
function resetForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        // Remover classes de validação
        const inputs = form.querySelectorAll('.is-invalid, .is-valid');
        inputs.forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });
    }
}

function setFormLoading(formId, loading = true) {
    const form = document.getElementById(formId);
    if (form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea');

        if (loading) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processando...';
            inputs.forEach(input => input.disabled = true);
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'Salvar';
            inputs.forEach(input => input.disabled = false);
        }
    }
}

// Exportar funções globais
window.BackupSystem = {
    showAlert,
    apiRequest,
    formatBytes,
    formatDateTime,
    formatDuration,
    getStatusColor,
    validateForm,
    resetForm,
    setFormLoading,
    checkSystemStatus,
    showAbout
};
