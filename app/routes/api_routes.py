from flask import request, jsonify
from datetime import datetime
from app import db
from app.models import BackupJob, BackupHistory, BackupConfig, SystemLog
from app.agents.backup_agent import BackupAgent
from . import api_bp

# ===== JOBS API =====
@api_bp.route('/jobs', methods=['GET'])
def get_jobs():
    """Listar todos os jobs de backup"""
    jobs = BackupJob.query.all()
    return jsonify([job.to_dict() for job in jobs])

@api_bp.route('/jobs', methods=['POST'])
def create_job():
    """Criar novo job de backup"""
    data = request.get_json()

    try:
        job = BackupJob(
            name=data['name'],
            description=data.get('description', ''),
            source_path=data['source_path'],
            destination_path=data['destination_path'],
            schedule_type=data.get('schedule_type', 'manual'),
            schedule_time=data.get('schedule_time'),
            schedule_days=data.get('schedule_days'),
            compression_enabled=data.get('compression_enabled', True),
            encryption_enabled=data.get('encryption_enabled', False),
            incremental_backup=data.get('incremental_backup', False),
            exclude_patterns=data.get('exclude_patterns')
        )

        db.session.add(job)
        db.session.commit()

        SystemLog.log('INFO', f'Job criado: {job.name}', 'api_routes', 'create_job', job_id=job.id)

        return jsonify(job.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        SystemLog.log('ERROR', f'Erro ao criar job: {str(e)}', 'api_routes', 'create_job')
        return jsonify({'error': str(e)}), 400

@api_bp.route('/jobs/<int:job_id>', methods=['GET'])
def get_job(job_id):
    """Obter job específico"""
    job = BackupJob.query.get_or_404(job_id)
    return jsonify(job.to_dict())

@api_bp.route('/jobs/<int:job_id>', methods=['PUT'])
def update_job(job_id):
    """Atualizar job existente"""
    job = BackupJob.query.get_or_404(job_id)
    data = request.get_json()

    try:
        # Atualizar campos
        for field in ['name', 'description', 'source_path', 'destination_path',
                     'schedule_type', 'schedule_time', 'schedule_days',
                     'compression_enabled', 'encryption_enabled', 'incremental_backup',
                     'exclude_patterns', 'is_active']:
            if field in data:
                setattr(job, field, data[field])

        job.updated_at = datetime.utcnow()
        db.session.commit()

        SystemLog.log('INFO', f'Job atualizado: {job.name}', 'api_routes', 'update_job', job_id=job.id)

        return jsonify(job.to_dict())

    except Exception as e:
        db.session.rollback()
        SystemLog.log('ERROR', f'Erro ao atualizar job {job_id}: {str(e)}', 'api_routes', 'update_job')
        return jsonify({'error': str(e)}), 400

@api_bp.route('/jobs/<int:job_id>', methods=['DELETE'])
def delete_job(job_id):
    """Deletar job"""
    job = BackupJob.query.get_or_404(job_id)

    try:
        job_name = job.name
        db.session.delete(job)
        db.session.commit()

        SystemLog.log('INFO', f'Job deletado: {job_name}', 'api_routes', 'delete_job', job_id=job_id)

        return jsonify({'message': 'Job deletado com sucesso'})

    except Exception as e:
        db.session.rollback()
        SystemLog.log('ERROR', f'Erro ao deletar job {job_id}: {str(e)}', 'api_routes', 'delete_job')
        return jsonify({'error': str(e)}), 400

@api_bp.route('/jobs/<int:job_id>/run', methods=['POST'])
def run_job(job_id):
    """Executar job manualmente"""
    job = BackupJob.query.get_or_404(job_id)

    try:
        agent = BackupAgent()
        result = agent.run_backup(job)

        return jsonify({
            'message': 'Backup iniciado com sucesso',
            'history_id': result.get('history_id')
        })

    except Exception as e:
        SystemLog.log('ERROR', f'Erro ao executar job {job_id}: {str(e)}', 'api_routes', 'run_job')
        return jsonify({'error': str(e)}), 400

# ===== HISTORY API =====
@api_bp.route('/history', methods=['GET'])
def get_history():
    """Listar histórico de backups"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    job_id = request.args.get('job_id', type=int)

    query = BackupHistory.query
    if job_id:
        query = query.filter_by(job_id=job_id)

    history = query.order_by(BackupHistory.started_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'items': [h.to_dict() for h in history.items],
        'total': history.total,
        'pages': history.pages,
        'current_page': page
    })

@api_bp.route('/history/<int:history_id>', methods=['GET'])
def get_history_detail(history_id):
    """Obter detalhes de um backup específico"""
    history = BackupHistory.query.get_or_404(history_id)
    return jsonify(history.to_dict())

# ===== LOGS API =====
@api_bp.route('/logs', methods=['GET'])
def get_logs():
    """Listar logs do sistema"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    level = request.args.get('level')

    query = SystemLog.query
    if level:
        query = query.filter_by(level=level.upper())

    logs = query.order_by(SystemLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'items': [log.to_dict() for log in logs.items],
        'total': logs.total,
        'pages': logs.pages,
        'current_page': page
    })

# ===== CONFIG API =====
@api_bp.route('/config', methods=['GET'])
def get_config():
    """Obter todas as configurações"""
    configs = BackupConfig.query.all()
    return jsonify([config.to_dict() for config in configs])

@api_bp.route('/config/<key>', methods=['GET'])
def get_config_value(key):
    """Obter valor de configuração específica"""
    value = BackupConfig.get_value(key)
    return jsonify({'key': key, 'value': value})

@api_bp.route('/config/<key>', methods=['PUT'])
def set_config_value(key):
    """Definir valor de configuração"""
    data = request.get_json()

    try:
        config = BackupConfig.set_value(
            key=key,
            value=data['value'],
            description=data.get('description'),
            config_type=data.get('config_type', 'string')
        )

        SystemLog.log('INFO', f'Configuração atualizada: {key}', 'api_routes', 'set_config_value')

        return jsonify(config.to_dict())

    except Exception as e:
        SystemLog.log('ERROR', f'Erro ao atualizar configuração {key}: {str(e)}', 'api_routes', 'set_config_value')
        return jsonify({'error': str(e)}), 400

# ===== DASHBOARD API =====
@api_bp.route('/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Obter estatísticas para o dashboard"""
    try:
        total_jobs = BackupJob.query.count()
        active_jobs = BackupJob.query.filter_by(is_active=True).count()

        # Últimos backups
        recent_backups = BackupHistory.query.order_by(
            BackupHistory.started_at.desc()
        ).limit(5).all()

        # Estatísticas de status
        completed_today = BackupHistory.query.filter(
            BackupHistory.started_at >= datetime.utcnow().date(),
            BackupHistory.status == 'completed'
        ).count()

        failed_today = BackupHistory.query.filter(
            BackupHistory.started_at >= datetime.utcnow().date(),
            BackupHistory.status == 'failed'
        ).count()

        return jsonify({
            'total_jobs': total_jobs,
            'active_jobs': active_jobs,
            'completed_today': completed_today,
            'failed_today': failed_today,
            'recent_backups': [backup.to_dict() for backup in recent_backups]
        })

    except Exception as e:
        SystemLog.log('ERROR', f'Erro ao obter estatísticas: {str(e)}', 'api_routes', 'get_dashboard_stats')
        return jsonify({'error': str(e)}), 400
