from flask import render_template, request, jsonify
from . import main_bp

@main_bp.route('/')
def index():
    """Página principal do dashboard"""
    return render_template('index.html')

@main_bp.route('/jobs')
def jobs():
    """Página de gerenciamento de jobs"""
    return render_template('jobs.html')

@main_bp.route('/history')
def history():
    """Página de histórico de backups"""
    return render_template('history.html')

@main_bp.route('/settings')
def settings():
    """Página de configurações"""
    return render_template('settings.html')

@main_bp.route('/logs')
def logs():
    """Página de logs do sistema"""
    return render_template('logs.html')
